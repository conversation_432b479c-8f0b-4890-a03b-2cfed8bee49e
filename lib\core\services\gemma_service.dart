import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter_gemma/flutter_gemma.dart';
import 'package:flutter_gemma/core/chat.dart';
import 'package:flutter_gemma/core/model.dart';
import 'package:flutter_gemma/core/message.dart';

import '../app_config.dart';
import '../models/translation_result.dart';
import '../models/language_info.dart';
import '../utils/logger.dart';
import 'cache_service.dart';
import 'performance_service.dart';

/// Enhanced Gemma 3N service with advanced multimodal capabilities
class GemmaService {
  static final GemmaService _instance = GemmaService._internal();
  static GemmaService get instance => _instance;
  GemmaService._internal();

  InferenceModel? _model;
  InferenceChat? _chat;
  bool _isInitialized = false;
  bool _isInitializing = false;
  String _currentModelName = AppConfig.defaultModelName;
  
  final StreamController<double> _downloadProgressController = StreamController<double>.broadcast();
  final StreamController<String> _statusController = StreamController<String>.broadcast();
  
  Stream<double> get downloadProgress => _downloadProgressController.stream;
  Stream<String> get status => _statusController.stream;
  
  bool get isInitialized => _isInitialized;
  bool get isInitializing => _isInitializing;
  String get currentModelName => _currentModelName;

  /// Pre-warm the service (optional background initialization)
  void preWarm() {
    if (!_isInitialized && !_isInitializing) {
      _initializeInBackground();
    }
  }

  /// Initialize Gemma 3N model with optimizations
  Future<bool> initialize({
    String? modelName,
    String? huggingFaceToken,
    bool forceReinitialization = false,
  }) async {
    if (_isInitialized && !forceReinitialization) {
      return true;
    }
    
    if (_isInitializing) {
      // Wait for current initialization to complete
      while (_isInitializing) {
        await Future.delayed(const Duration(milliseconds: 100));
      }
      return _isInitialized;
    }
    
    _isInitializing = true;
    _statusController.add('Inicializando Gemma 3N...');
    
    try {
      final targetModel = modelName ?? AppConfig.defaultModelName;
      _currentModelName = targetModel;
      
      // Check if model is already downloaded
      final gemma = FlutterGemmaPlugin.instance;
      final modelManager = gemma.modelManager;
      
      bool isModelAvailable = await _checkModelAvailability(targetModel);
      
      if (!isModelAvailable && huggingFaceToken != null) {
        _statusController.add('Baixando modelo $targetModel...');
        await _downloadModel(targetModel, huggingFaceToken);
      }
      
      _statusController.add('Carregando modelo...');
      
      // Create model with optimized settings
      _model = await gemma.createModel(
        modelType: ModelType.gemmaIt,
        maxTokens: AppConfig.maxTokens,
        supportImage: true,
        maxNumImages: 5, // Support multiple images
      );
      
      _statusController.add('Criando sessão de chat...');
      
      // Create optimized chat session
      _chat = await _model!.createChat(
        temperature: AppConfig.defaultTemperature,
        topK: AppConfig.defaultTopK,
        topP: AppConfig.defaultTopP,
        supportImage: true,
        tokenBuffer: 512, // Larger buffer for better context
      );
      
      _isInitialized = true;
      _statusController.add('Gemma 3N inicializado com sucesso!');
      
      Logger.info('Gemma 3N initialized successfully with model: $targetModel');
      
      // Record performance metrics
      PerformanceService.instance.recordModelInitialization(targetModel);
      
      return true;
      
    } catch (e) {
      Logger.error('Failed to initialize Gemma 3N: $e');
      _statusController.add('Erro na inicialização: $e');
      return false;
    } finally {
      _isInitializing = false;
    }
  }

  /// Translate text with advanced language detection and optimization
  Future<TranslationResult> translateText({
    required String text,
    required String targetLanguage,
    String? sourceLanguage,
    Map<String, dynamic>? context,
  }) async {
    if (!_isInitialized) {
      throw Exception('Gemma service not initialized');
    }
    
    final startTime = DateTime.now();
    
    try {
      // Check cache first
      final cacheKey = _generateCacheKey(text, sourceLanguage, targetLanguage);
      final cachedResult = await CacheService.instance.getTranslation(cacheKey);
      
      if (cachedResult != null) {
        Logger.info('Translation served from cache');
        return cachedResult;
      }
      
      // Build optimized prompt for Gemma 3N
      final prompt = _buildTranslationPrompt(
        text: text,
        targetLanguage: targetLanguage,
        sourceLanguage: sourceLanguage,
        context: context,
      );
      
      // Create message
      final message = Message(text: prompt, isUser: true);
      
      // Add to chat and get response
      await _chat!.addQueryChunk(message);
      final responseStream = _chat!.generateChatResponseAsync();
      
      String fullResponse = '';
      await for (final token in responseStream) {
        fullResponse += token;
      }
      
      // Parse response and create result
      final result = _parseTranslationResponse(
        fullResponse,
        text,
        targetLanguage,
        sourceLanguage,
      );
      
      // Calculate performance metrics
      final duration = DateTime.now().difference(startTime);
      result.processingTime = duration;
      
      // Cache the result
      await CacheService.instance.cacheTranslation(cacheKey, result);
      
      // Record metrics
      PerformanceService.instance.recordTranslation(
        sourceLanguage: sourceLanguage ?? 'auto',
        targetLanguage: targetLanguage,
        textLength: text.length,
        processingTime: duration,
      );
      
      Logger.info('Text translation completed in ${duration.inMilliseconds}ms');
      
      return result;
      
    } catch (e) {
      Logger.error('Text translation failed: $e');
      rethrow;
    }
  }

  /// Translate image with OCR and context understanding
  Future<TranslationResult> translateImage({
    required Uint8List imageBytes,
    required String targetLanguage,
    String? sourceLanguage,
    String? additionalContext,
  }) async {
    if (!_isInitialized) {
      throw Exception('Gemma service not initialized');
    }
    
    final startTime = DateTime.now();
    
    try {
      // Build image translation prompt
      final prompt = _buildImageTranslationPrompt(
        targetLanguage: targetLanguage,
        sourceLanguage: sourceLanguage,
        additionalContext: additionalContext,
      );
      
      // Create message with image
      final message = Message.withImage(
        text: prompt,
        imageBytes: imageBytes,
        isUser: true,
      );
      
      // Process with Gemma 3N
      await _chat!.addQueryChunk(message);
      final responseStream = _chat!.generateChatResponseAsync();
      
      String fullResponse = '';
      await for (final token in responseStream) {
        fullResponse += token;
      }
      
      // Parse and create result
      final result = _parseImageTranslationResponse(
        fullResponse,
        targetLanguage,
        sourceLanguage,
      );
      
      result.processingTime = DateTime.now().difference(startTime);
      
      Logger.info('Image translation completed in ${result.processingTime!.inMilliseconds}ms');
      
      return result;
      
    } catch (e) {
      Logger.error('Image translation failed: $e');
      rethrow;
    }
  }

  /// Get supported languages with metadata
  List<LanguageInfo> getSupportedLanguages() {
    return LanguageInfo.getAllSupportedLanguages();
  }

  /// Check model health and performance
  Future<Map<String, dynamic>> getModelInfo() async {
    return {
      'model_name': _currentModelName,
      'is_initialized': _isInitialized,
      'supports_multimodal': true,
      'max_tokens': AppConfig.maxTokens,
      'context_window': AppConfig.contextWindow,
      'supported_languages': getSupportedLanguages().length,
      'cache_size': await CacheService.instance.getCacheSize(),
      'performance_metrics': PerformanceService.instance.getMetrics(),
    };
  }

  /// Dispose resources
  void dispose() {
    _downloadProgressController.close();
    _statusController.close();
    _model = null;
    _chat = null;
    _isInitialized = false;
  }

  // Private helper methods
  Future<void> _initializeInBackground() async {
    try {
      await initialize();
    } catch (e) {
      Logger.warning('Background initialization failed: $e');
    }
  }

  Future<bool> _checkModelAvailability(String modelName) async {
    // Implementation depends on flutter_gemma's model manager
    // This is a placeholder for the actual check
    return false;
  }

  Future<void> _downloadModel(String modelName, String token) async {
    // Implementation for model download with progress tracking
    // This would use flutter_gemma's download capabilities
    _downloadProgressController.add(0.0);
    
    // Simulate download progress
    for (int i = 0; i <= 100; i += 10) {
      await Future.delayed(const Duration(milliseconds: 500));
      _downloadProgressController.add(i / 100.0);
    }
  }

  String _generateCacheKey(String text, String? sourceLanguage, String targetLanguage) {
    return '${sourceLanguage ?? 'auto'}_${targetLanguage}_${text.hashCode}';
  }

  String _buildTranslationPrompt({
    required String text,
    required String targetLanguage,
    String? sourceLanguage,
    Map<String, dynamic>? context,
  }) {
    final languageInfo = LanguageInfo.getLanguageInfo(targetLanguage);
    
    String prompt = '''
You are an expert translator specializing in ${languageInfo?.name ?? targetLanguage}.
Translate the following text accurately while preserving meaning, tone, and cultural context.

Text to translate: "$text"
Target language: ${languageInfo?.name ?? targetLanguage}
''';

    if (sourceLanguage != null && sourceLanguage != 'auto') {
      final sourceInfo = LanguageInfo.getLanguageInfo(sourceLanguage);
      prompt += 'Source language: ${sourceInfo?.name ?? sourceLanguage}\n';
    }

    if (languageInfo?.isTonal == true) {
      prompt += 'Note: This is a tonal language. Pay attention to tone markers.\n';
    }

    if (languageInfo?.hasComplexScript == true) {
      prompt += 'Note: This language uses a complex script. Ensure proper rendering.\n';
    }

    if (context != null) {
      prompt += 'Additional context: ${context.toString()}\n';
    }

    prompt += '''
Provide only the translation without explanations or additional text.
''';

    return prompt;
  }

  String _buildImageTranslationPrompt({
    required String targetLanguage,
    String? sourceLanguage,
    String? additionalContext,
  }) {
    final languageInfo = LanguageInfo.getLanguageInfo(targetLanguage);
    
    String prompt = '''
You are an expert at extracting and translating text from images.
Analyze this image and:
1. Extract all visible text
2. Translate it to ${languageInfo?.name ?? targetLanguage}
3. Preserve formatting and structure
4. Provide cultural context if relevant

Target language: ${languageInfo?.name ?? targetLanguage}
''';

    if (additionalContext != null) {
      prompt += 'Additional context: $additionalContext\n';
    }

    return prompt;
  }

  TranslationResult _parseTranslationResponse(
    String response,
    String originalText,
    String targetLanguage,
    String? sourceLanguage,
  ) {
    // Parse the Gemma 3N response and extract translation
    // This is a simplified implementation
    return TranslationResult(
      originalText: originalText,
      translatedText: response.trim(),
      sourceLanguage: sourceLanguage ?? 'auto',
      targetLanguage: targetLanguage,
      confidence: 0.95, // Would be calculated based on model output
      timestamp: DateTime.now(),
    );
  }

  TranslationResult _parseImageTranslationResponse(
    String response,
    String targetLanguage,
    String? sourceLanguage,
  ) {
    // Parse image translation response
    return TranslationResult(
      originalText: '[Image Content]',
      translatedText: response.trim(),
      sourceLanguage: sourceLanguage ?? 'auto',
      targetLanguage: targetLanguage,
      confidence: 0.90,
      timestamp: DateTime.now(),
      isImageTranslation: true,
    );
  }
}
