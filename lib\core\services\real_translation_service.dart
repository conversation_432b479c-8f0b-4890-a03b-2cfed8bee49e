import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:flutter/foundation.dart';

import '../models/translation_result.dart';
import '../models/image_message.dart';
import '../config/api_keys.dart';

/// Real translation service using Google Generative AI (Gemma 3M)
class RealTranslationService {
  static final RealTranslationService _instance = RealTranslationService._internal();
  static RealTranslationService get instance => _instance;
  RealTranslationService._internal();

  GenerativeModel? _model;
  bool _isInitialized = false;
  String _currentModel = '';

  bool get isInitialized => _isInitialized;
  String get currentModel => _currentModel;

  /// Initialize the translation service with Gemma 3M
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize environment variables first
      await ApiKeys.initialize();

      if (!ApiKeys.isGoogleAIConfigured) {
        print('Google AI API key not configured. Using fallback translation.');
        print('Please add GEMINI_API_KEY to your .env file');
        _isInitialized = true;
        return;
      }

      // Use Gemma 3M model specifically
      _currentModel = ApiKeys.geminiModel;

      _model = GenerativeModel(
        model: _currentModel,
        apiKey: ApiKeys.googleAI,
        generationConfig: GenerationConfig(
          temperature: ApiKeys.temperature,
          topK: 1,
          topP: 0.9,
          maxOutputTokens: ApiKeys.maxTokens,
        ),
        safetySettings: [
          SafetySetting(HarmCategory.harassment, HarmBlockThreshold.medium),
          SafetySetting(HarmCategory.hateSpeech, HarmBlockThreshold.medium),
          SafetySetting(HarmCategory.sexuallyExplicit, HarmBlockThreshold.medium),
          SafetySetting(HarmCategory.dangerousContent, HarmBlockThreshold.medium),
        ],
      );

      _isInitialized = true;
      if (kDebugMode) {
        print('✅ Real translation service initialized with $_currentModel');
        print('🤖 Using Gemma 3M for high-quality translations');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize real translation service: $e');
      }
      _isInitialized = true; // Allow fallback
    }
  }

  /// Translate text using Google Generative AI
  Future<TranslationResult> translateText({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
    String? context,
    String? domain,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      // Perform translation
      final result = await _performTranslation(
        text: text,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        context: context,
        domain: domain,
      );

      return result;

    } catch (e) {
      if (kDebugMode) {
        print('Translation failed: $e');
      }

      // Return fallback translation
      return TranslationResult(
        originalText: text,
        translatedText: 'Erro na tradução: $e',
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        confidence: 0.0,
        timestamp: DateTime.now(),
        metadata: {'error': e.toString()},
      );
    }
  }

  /// Perform the actual translation
  Future<TranslationResult> _performTranslation({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
    String? context,
    String? domain,
  }) async {
    if (_model == null || !ApiKeys.isGoogleAIConfigured) {
      return _fallbackTranslation(text, sourceLanguage, targetLanguage);
    }

    try {
      final prompt = _buildTranslationPrompt(
        text: text,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        context: context,
        domain: domain,
      );

      final content = [Content.text(prompt)];
      final response = await _model!.generateContent(content);

      if (response.text == null || response.text!.isEmpty) {
        throw Exception('Empty response from AI model');
      }

      final translatedText = _extractTranslation(response.text!);
      final confidence = _calculateConfidence(response);

      return TranslationResult(
        originalText: text,
        translatedText: translatedText,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        confidence: confidence,
        timestamp: DateTime.now(),
        metadata: {
          'model': _currentModel,
          'method': 'gemma_3m_ai',
          'api': 'google_generative_ai',
          'temperature': ApiKeys.temperature,
          'max_tokens': ApiKeys.maxTokens,
          'service': 'real_translation',
        },
      );

    } catch (e) {
      print('AI translation failed: $e');
      return _fallbackTranslation(text, sourceLanguage, targetLanguage);
    }
  }

  /// Build optimized translation prompt for Gemma 3M
  String _buildTranslationPrompt({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
    String? context,
    String? domain,
  }) {
    final sourceLang = _getLanguageName(sourceLanguage);
    final targetLang = _getLanguageName(targetLanguage);

    final buffer = StringBuffer();

    // Optimized prompt for Gemma 3M
    buffer.writeln('# Professional Translation Task');
    buffer.writeln();
    buffer.writeln('You are Gemma 3M, an advanced multilingual AI specialized in high-quality translation.');
    buffer.writeln('Your task: Translate text from $sourceLang to $targetLang with maximum accuracy and naturalness.');
    buffer.writeln();

    if (domain != null && domain.isNotEmpty) {
      buffer.writeln('**Domain**: $domain');
      buffer.writeln('Use specialized terminology appropriate for this field.');
      buffer.writeln();
    }

    if (context != null && context.isNotEmpty) {
      buffer.writeln('**Context**: $context');
      buffer.writeln('Consider this context for accurate translation.');
      buffer.writeln();
    }

    buffer.writeln('## Translation Guidelines:');
    buffer.writeln('- Provide ONLY the direct translation');
    buffer.writeln('- Maintain original tone, style, and register');
    buffer.writeln('- Preserve all formatting and punctuation');
    buffer.writeln('- Use natural, idiomatic expressions in target language');
    buffer.writeln('- Adapt cultural references appropriately');
    buffer.writeln('- Handle technical terms with precision');
    buffer.writeln('- Ensure grammatical correctness');
    buffer.writeln();

    buffer.writeln('## Source Text ($sourceLang):');
    buffer.writeln('```');
    buffer.writeln(text);
    buffer.writeln('```');
    buffer.writeln();
    buffer.writeln('## Translation ($targetLang):');

    return buffer.toString();
  }

  /// Extract translation from AI response
  String _extractTranslation(String response) {
    // Remove common AI response patterns
    String cleaned = response.trim();
    
    // Remove quotes if the entire response is quoted
    if (cleaned.startsWith('"') && cleaned.endsWith('"')) {
      cleaned = cleaned.substring(1, cleaned.length - 1);
    }
    
    // Remove "Translation:" prefix if present
    if (cleaned.toLowerCase().startsWith('translation:')) {
      cleaned = cleaned.substring(12).trim();
    }
    
    return cleaned;
  }

  /// Calculate confidence score based on response
  double _calculateConfidence(GenerateContentResponse response) {
    // Basic confidence calculation
    // In a real implementation, you might use more sophisticated metrics
    
    if (response.text == null || response.text!.isEmpty) {
      return 0.0;
    }
    
    double confidence = 0.8; // Base confidence for AI translation
    
    // Adjust based on response length vs input length
    final responseLength = response.text!.length;
    if (responseLength > 10) {
      confidence += 0.1;
    }
    
    // Check for common error indicators
    final text = response.text!.toLowerCase();
    if (text.contains('sorry') || text.contains('cannot') || text.contains('error')) {
      confidence -= 0.3;
    }
    
    return confidence.clamp(0.0, 1.0);
  }

  /// Fallback translation when AI is not available
  TranslationResult _fallbackTranslation(String text, String sourceLanguage, String targetLanguage) {
    // Simple rule-based fallback
    String translatedText;
    
    if (targetLanguage == 'pt') {
      translatedText = _translateToPortuguese(text, sourceLanguage);
    } else if (targetLanguage == 'en') {
      translatedText = _translateToEnglish(text, sourceLanguage);
    } else {
      translatedText = '[Tradução para ${_getLanguageName(targetLanguage)}] $text';
    }
    
    return TranslationResult(
      originalText: text,
      translatedText: translatedText,
      sourceLanguage: sourceLanguage,
      targetLanguage: targetLanguage,
      confidence: 0.6,
      timestamp: DateTime.now(),
      metadata: {'method': 'rule_based_fallback'},
    );
  }

  /// Simple Portuguese translation fallback
  String _translateToPortuguese(String text, String sourceLanguage) {
    final commonTranslations = {
      'hello': 'olá',
      'goodbye': 'tchau',
      'thank you': 'obrigado',
      'please': 'por favor',
      'yes': 'sim',
      'no': 'não',
      'good morning': 'bom dia',
      'good afternoon': 'boa tarde',
      'good evening': 'boa noite',
      'how are you': 'como você está',
      'what is your name': 'qual é o seu nome',
      'my name is': 'meu nome é',
      'nice to meet you': 'prazer em conhecê-lo',
      'excuse me': 'com licença',
      'sorry': 'desculpe',
      'help': 'ajuda',
      'water': 'água',
      'food': 'comida',
      'house': 'casa',
      'car': 'carro',
      'book': 'livro',
      'computer': 'computador',
      'phone': 'telefone',
      'money': 'dinheiro',
      'time': 'tempo',
      'day': 'dia',
      'night': 'noite',
      'love': 'amor',
      'friend': 'amigo',
      'family': 'família',
    };
    
    String result = text.toLowerCase();
    for (final entry in commonTranslations.entries) {
      result = result.replaceAll(entry.key, entry.value);
    }
    
    return result;
  }

  /// Simple English translation fallback
  String _translateToEnglish(String text, String sourceLanguage) {
    final commonTranslations = {
      'olá': 'hello',
      'tchau': 'goodbye',
      'obrigado': 'thank you',
      'obrigada': 'thank you',
      'por favor': 'please',
      'sim': 'yes',
      'não': 'no',
      'bom dia': 'good morning',
      'boa tarde': 'good afternoon',
      'boa noite': 'good evening',
      'como você está': 'how are you',
      'qual é o seu nome': 'what is your name',
      'meu nome é': 'my name is',
      'prazer em conhecê-lo': 'nice to meet you',
      'com licença': 'excuse me',
      'desculpe': 'sorry',
      'ajuda': 'help',
      'água': 'water',
      'comida': 'food',
      'casa': 'house',
      'carro': 'car',
      'livro': 'book',
      'computador': 'computer',
      'telefone': 'phone',
      'dinheiro': 'money',
      'tempo': 'time',
      'dia': 'day',
      'noite': 'night',
      'amor': 'love',
      'amigo': 'friend',
      'família': 'family',
    };
    
    String result = text.toLowerCase();
    for (final entry in commonTranslations.entries) {
      result = result.replaceAll(entry.key, entry.value);
    }
    
    return result;
  }

  /// Get language name from code
  String _getLanguageName(String code) {
    final languages = {
      'auto': 'Auto-detect',
      'en': 'English',
      'pt': 'Portuguese',
      'es': 'Spanish',
      'fr': 'French',
      'de': 'German',
      'it': 'Italian',
      'ja': 'Japanese',
      'ko': 'Korean',
      'zh': 'Chinese',
      'ar': 'Arabic',
      'ru': 'Russian',
      'hi': 'Hindi',
    };
    
    return languages[code] ?? code.toUpperCase();
  }

  /// Generate cache key for translation
  String _generateCacheKey(String text, String sourceLanguage, String targetLanguage, String? context, String? domain) {
    final components = [
      text,
      sourceLanguage,
      targetLanguage,
      context ?? '',
      domain ?? '',
    ];
    
    return components.join('|');
  }

  /// Detect language of text
  Future<String> detectLanguage(String text) async {
    if (!_isInitialized) {
      await initialize();
    }

    if (_model == null || !ApiKeys.isGoogleAIConfigured) {
      return _fallbackLanguageDetection(text);
    }

    try {
      final prompt = '''
Detect the language of the following text and respond with ONLY the ISO 639-1 language code (2 letters).

Examples:
- For English text, respond: en
- For Portuguese text, respond: pt
- For Spanish text, respond: es
- For French text, respond: fr

Text: "$text"

Language code:''';

      final content = [Content.text(prompt)];
      final response = await _model!.generateContent(content);

      if (response.text != null && response.text!.isNotEmpty) {
        final detected = response.text!.trim().toLowerCase();
        if (detected.length == 2 && RegExp(r'^[a-z]{2}$').hasMatch(detected)) {
          return detected;
        }
      }

      return _fallbackLanguageDetection(text);

    } catch (e) {
      print('Language detection failed: $e');
      return _fallbackLanguageDetection(text);
    }
  }

  /// Fallback language detection
  String _fallbackLanguageDetection(String text) {
    // Simple heuristic-based language detection
    final lowerText = text.toLowerCase();

    // Portuguese indicators
    if (lowerText.contains(RegExp(r'\b(o|a|os|as|um|uma|de|da|do|em|na|no|para|com|por|que|não|sim|muito|bem|como|onde|quando|porque)\b'))) {
      return 'pt';
    }

    // Spanish indicators
    if (lowerText.contains(RegExp(r'\b(el|la|los|las|un|una|de|del|en|con|por|que|no|sí|muy|bien|como|donde|cuando|porque)\b'))) {
      return 'es';
    }

    // French indicators
    if (lowerText.contains(RegExp(r'\b(le|la|les|un|une|de|du|dans|avec|par|que|non|oui|très|bien|comment|où|quand|parce)\b'))) {
      return 'fr';
    }

    // Default to English
    return 'en';
  }

  /// Translate text from image using Google Generative AI
  Future<TranslationResult> translateImage({
    required Uint8List imageBytes,
    required String targetLanguage,
    String? sourceLanguage,
    String? additionalContext,
  }) async {
    print('🔧 translateImage called with:');
    print('   - Image size: ${imageBytes.length} bytes');
    print('   - Target language: $targetLanguage');
    print('   - Source language: $sourceLanguage');
    print('   - Context: $additionalContext');

    if (!_isInitialized) {
      print('⚠️ Service not initialized, initializing...');
      await initialize();
    }

    try {
      print('🚀 Calling _performImageTranslation...');
      final result = await _performImageTranslation(
        imageBytes: imageBytes,
        targetLanguage: targetLanguage,
        sourceLanguage: sourceLanguage,
        additionalContext: additionalContext,
      );

      print('✅ Translation successful!');
      return result;

    } catch (e) {
      print('❌ Image translation failed: $e');

      // Return fallback result
      return TranslationResult(
        originalText: 'Imagem selecionada',
        translatedText: 'Erro na tradução de imagem: $e',
        sourceLanguage: sourceLanguage ?? 'auto',
        targetLanguage: targetLanguage,
        confidence: 0.0,
        timestamp: DateTime.now(),
        metadata: {'error': e.toString(), 'type': 'image_translation'},
      );
    }
  }

  /// Perform the actual image translation
  Future<TranslationResult> _performImageTranslation({
    required Uint8List imageBytes,
    required String targetLanguage,
    String? sourceLanguage,
    String? additionalContext,
  }) async {
    if (_model == null || !ApiKeys.isGoogleAIConfigured) {
      return _fallbackImageTranslation(imageBytes, targetLanguage, sourceLanguage);
    }

    try {
      final prompt = _buildImageTranslationPrompt(
        targetLanguage: targetLanguage,
        sourceLanguage: sourceLanguage,
        additionalContext: additionalContext,
      );

      // Create content with image and text
      final content = [
        Content.multi([
          TextPart(prompt),
          DataPart('image/jpeg', imageBytes),
        ])
      ];

      final response = await _model!.generateContent(content);

      if (response.text == null || response.text!.isEmpty) {
        throw Exception('Empty response from AI model');
      }

      final extractedAndTranslated = _extractImageTranslationResult(response.text!);
      final confidence = _calculateImageConfidence(response, extractedAndTranslated);

      return TranslationResult(
        originalText: extractedAndTranslated['extracted_text'] ?? 'Texto extraído da imagem',
        translatedText: extractedAndTranslated['translated_text'] ?? response.text!,
        sourceLanguage: extractedAndTranslated['detected_language'] ?? sourceLanguage ?? 'auto',
        targetLanguage: targetLanguage,
        confidence: confidence,
        timestamp: DateTime.now(),
        metadata: {
          'model': _currentModel,
          'method': 'image_translation',
          'api': 'google_generative_ai',
          'temperature': ApiKeys.temperature,
          'max_tokens': ApiKeys.maxTokens,
          'service': 'real_image_translation',
          'extracted_text': extractedAndTranslated['extracted_text'],
          'detected_language': extractedAndTranslated['detected_language'],
        },
      );

    } catch (e) {
      print('AI image translation failed: $e');
      return _fallbackImageTranslation(imageBytes, targetLanguage, sourceLanguage);
    }
  }

  /// Build optimized image translation prompt for Gemma
  String _buildImageTranslationPrompt({
    required String targetLanguage,
    String? sourceLanguage,
    String? additionalContext,
  }) {
    final targetLang = _getLanguageName(targetLanguage);
    final sourceLang = sourceLanguage != null ? _getLanguageName(sourceLanguage) : 'any language';

    final buffer = StringBuffer();

    buffer.writeln('# Image Text Translation Task');
    buffer.writeln();
    buffer.writeln('You are an expert at reading text from images and translating it accurately.');
    buffer.writeln('Your task: Extract all visible text from this image and translate it to $targetLang.');
    buffer.writeln();

    if (additionalContext != null && additionalContext.isNotEmpty) {
      buffer.writeln('**Context**: $additionalContext');
      buffer.writeln();
    }

    buffer.writeln('## Instructions:');
    buffer.writeln('1. **Extract**: Read all visible text from the image carefully');
    buffer.writeln('2. **Detect**: Identify the source language of the text');
    buffer.writeln('3. **Translate**: Convert the text to $targetLang naturally');
    buffer.writeln('4. **Format**: Provide your response in this exact format:');
    buffer.writeln();
    buffer.writeln('```');
    buffer.writeln('EXTRACTED_TEXT: [all text found in the image]');
    buffer.writeln('DETECTED_LANGUAGE: [source language detected]');
    buffer.writeln('TRANSLATED_TEXT: [translation in $targetLang]');
    buffer.writeln('```');
    buffer.writeln();
    buffer.writeln('## Guidelines:');
    buffer.writeln('- Extract text exactly as it appears (preserve formatting)');
    buffer.writeln('- If no text is visible, respond with "No text detected"');
    buffer.writeln('- Maintain context and meaning in translation');
    buffer.writeln('- Use natural, fluent language in the target language');
    buffer.writeln('- For menus, preserve food names and descriptions appropriately');

    return buffer.toString();
  }

  /// Extract translation result from AI response
  Map<String, String> _extractImageTranslationResult(String response) {
    final result = <String, String>{};

    // Extract using regex patterns
    final extractedMatch = RegExp(r'EXTRACTED_TEXT:\s*(.+?)(?=\n|DETECTED_LANGUAGE:|TRANSLATED_TEXT:|$)',
        multiLine: true, dotAll: true).firstMatch(response);
    final languageMatch = RegExp(r'DETECTED_LANGUAGE:\s*(.+?)(?=\n|TRANSLATED_TEXT:|$)',
        multiLine: true).firstMatch(response);
    final translatedMatch = RegExp(r'TRANSLATED_TEXT:\s*(.+?)(?=\n|$)',
        multiLine: true, dotAll: true).firstMatch(response);

    result['extracted_text'] = extractedMatch?.group(1)?.trim() ?? '';
    result['detected_language'] = languageMatch?.group(1)?.trim() ?? 'auto';
    result['translated_text'] = translatedMatch?.group(1)?.trim() ?? response.trim();

    // If structured format not found, use the entire response as translation
    if (result['translated_text']!.isEmpty) {
      result['translated_text'] = response.trim();
    }

    return result;
  }

  /// Calculate confidence for image translation
  double _calculateImageConfidence(GenerateContentResponse response, Map<String, String> extractedData) {
    double confidence = 0.7; // Base confidence for image translation

    // Check if we have extracted text
    if (extractedData['extracted_text']?.isNotEmpty == true) {
      confidence += 0.1;
    }

    // Check if language was detected
    if (extractedData['detected_language'] != null &&
        extractedData['detected_language'] != 'auto' &&
        extractedData['detected_language']!.isNotEmpty) {
      confidence += 0.1;
    }

    // Check response quality
    if (response.text != null && response.text!.length > 20) {
      confidence += 0.1;
    }

    // Check for error indicators
    final text = response.text?.toLowerCase() ?? '';
    if (text.contains('no text') || text.contains('cannot') || text.contains('error')) {
      confidence -= 0.2;
    }

    return confidence.clamp(0.0, 1.0);
  }

  /// Fallback image translation when AI is not available
  TranslationResult _fallbackImageTranslation(Uint8List imageBytes, String targetLanguage, String? sourceLanguage) {
    final targetLang = _getLanguageName(targetLanguage);

    return TranslationResult(
      originalText: 'Imagem selecionada (${(imageBytes.length / 1024).toStringAsFixed(1)} KB)',
      translatedText: 'Tradução de imagem não disponível. Configure a API key do Google Gemini para usar esta funcionalidade.',
      sourceLanguage: sourceLanguage ?? 'auto',
      targetLanguage: targetLanguage,
      confidence: 0.3,
      timestamp: DateTime.now(),
      metadata: {
        'method': 'fallback_image',
        'image_size_bytes': imageBytes.length,
        'target_language': targetLang,
      },
    );
  }
}
