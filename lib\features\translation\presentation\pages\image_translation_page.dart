import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'dart:typed_data';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/app_providers.dart';
import '../../../../core/services/ollama_service.dart';
import '../../../../core/services/analytics_service.dart';
import '../../../../core/models/translation_result.dart';
import '../widgets/translation_result_card.dart';

/// Image translation page with camera and gallery support
class ImageTranslationPage extends ConsumerStatefulWidget {
  const ImageTranslationPage({super.key});

  @override
  ConsumerState<ImageTranslationPage> createState() => _ImageTranslationPageState();
}

class _ImageTranslationPageState extends ConsumerState<ImageTranslationPage>
    with AutomaticKeepAliveClientMixin {
  final ImagePicker _imagePicker = ImagePicker();
  
  File? _selectedImage;
  Uint8List? _imageBytes;
  TranslationResult? _currentResult;
  bool _isProcessing = false;
  String _processingStatus = '';

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    final theme = Theme.of(context);
    final languagePreferences = ref.watch(languagePreferencesProvider);
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Image Selection Section
          _buildImageSelectionSection(theme),
          
          const SizedBox(height: 16),
          
          // Selected Image Display
          if (_selectedImage != null)
            _buildImageDisplaySection(theme),
          
          const SizedBox(height: 16),
          
          // Translation Button
          if (_selectedImage != null)
            _buildTranslationButton(theme, languagePreferences),
          
          const SizedBox(height: 16),
          
          // Result Section
          if (_currentResult != null || _isProcessing)
            _buildResultSection(theme),
        ],
      ),
    );
  }

  Widget _buildImageSelectionSection(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(
              Icons.image,
              size: 64,
              color: AppTheme.primaryColor.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'Selecione uma imagem para traduzir',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Capture uma foto ou escolha da galeria',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _pickImage(ImageSource.camera),
                    icon: const Icon(Icons.camera_alt),
                    label: const Text('Câmera'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _pickImage(ImageSource.gallery),
                    icon: const Icon(Icons.photo_library),
                    label: const Text('Galeria'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageDisplaySection(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Imagem Selecionada',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: _clearImage,
                  icon: const Icon(Icons.close),
                  tooltip: 'Remover imagem',
                ),
              ],
            ),
            const SizedBox(height: 12),
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.file(
                _selectedImage!,
                width: double.infinity,
                height: 200,
                fit: BoxFit.cover,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Toque em "Traduzir Imagem" para extrair e traduzir o texto',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTranslationButton(ThemeData theme, LanguagePreferences preferences) {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _isProcessing ? null : () => _translateImage(preferences),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
          disabledBackgroundColor: Colors.grey[300],
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isProcessing
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text('Processando...'),
                ],
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.translate),
                  const SizedBox(width: 8),
                  Text(
                    'Traduzir Imagem',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildResultSection(ThemeData theme) {
    if (_isProcessing) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Enhanced loading animation
              Stack(
                alignment: Alignment.center,
                children: [
                  SizedBox(
                    width: 60,
                    height: 60,
                    child: CircularProgressIndicator(
                      strokeWidth: 3,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  Icon(
                    Icons.image_search,
                    size: 24,
                    color: theme.colorScheme.primary,
                  ),
                ],
              ),
              const SizedBox(height: 20),
              Text(
                'Processando Imagem',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _processingStatus.isNotEmpty
                    ? _processingStatus
                    : '🔍 Preparando análise...',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              LinearProgressIndicator(
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  theme.colorScheme.primary,
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (_currentResult == null) return const SizedBox.shrink();

    return TranslationResultCard(
      result: _currentResult!,
      onCopy: () => _copyToClipboard(_currentResult!.translatedText),
      onShare: () => _shareResult(_currentResult!),
      onSave: () => _saveToHistory(_currentResult!),
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        final imageBytes = await image.readAsBytes();
        setState(() {
          _selectedImage = File(image.path);
          _imageBytes = imageBytes;
          _currentResult = null;
        });

        AnalyticsService.instance.trackFeatureUsage(
          featureName: 'image_selection',
          parameters: {
            'source': source.name,
            'size_bytes': imageBytes.length,
          },
        );
      }
    } catch (e) {
      _showErrorSnackBar('Erro ao selecionar imagem: $e');
    }
  }

  void _clearImage() {
    setState(() {
      _selectedImage = null;
      _imageBytes = null;
      _currentResult = null;
    });
  }

  Future<void> _translateImage(LanguagePreferences preferences) async {
    if (_imageBytes == null || _isProcessing) return;

    setState(() {
      _isProcessing = true;
      _currentResult = null;
      _processingStatus = '🚀 Iniciando processamento...';
    });

    try {
      final result = await OllamaService.instance.translateImage(
        imageBytes: _imageBytes!,
        targetLanguage: preferences.targetLanguage,
        sourceLanguage: preferences.sourceLanguage != 'auto'
            ? preferences.sourceLanguage
            : null,
        additionalContext: 'Extract and translate any text found in this image',
        onStatusUpdate: (status) {
          setState(() {
            _processingStatus = status;
          });
        },
      );

      setState(() {
        _currentResult = result;
      });

      // Add to history
      ref.read(translationHistoryProvider.notifier).addTranslation(result);

      // Track analytics
      AnalyticsService.instance.trackTranslation(
        sourceLanguage: result.sourceLanguage,
        targetLanguage: result.targetLanguage,
        translationType: 'image',
        textLength: result.originalText.length,
        processingTime: result.processingTime ?? Duration.zero,
        confidence: result.confidence,
      );

    } catch (e) {
      _showErrorSnackBar('Erro na tradução da imagem: $e');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  Future<void> _copyToClipboard(String text) async {
    try {
      await Clipboard.setData(ClipboardData(text: text));
      _showSuccessSnackBar('Texto copiado!');
    } catch (e) {
      _showErrorSnackBar('Erro ao copiar: $e');
    }
  }

  void _shareResult(TranslationResult result) {
    _showInfoSnackBar('Compartilhamento em desenvolvimento');
  }

  void _saveToHistory(TranslationResult result) {
    ref.read(translationHistoryProvider.notifier).addTranslation(result);
    _showSuccessSnackBar('Salvo no histórico!');
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.successColor,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorColor,
      ),
    );
  }

  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.infoColor,
      ),
    );
  }
}
