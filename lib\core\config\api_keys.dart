import 'package:flutter_dotenv/flutter_dotenv.dart';

/// API Keys configuration using environment variables
///
/// IMPORTANT: API keys are loaded from .env file
/// Never commit real API keys to version control!
///
/// To get your Google AI API key:
/// 1. Go to https://makersuite.google.com/app/apikey
/// 2. Create a new API key
/// 3. Add it to .env file as GEMINI_API_KEY=your_key_here
///
class ApiKeys {
  /// Initialize environment variables
  static Future<void> initialize() async {
    try {
      await dotenv.load(fileName: ".env");
    } catch (e) {
      print('Warning: Could not load .env file: $e');
    }
  }

  /// Ollama configuration
  static String get ollamaHost => dotenv.env['OLLAMA_HOST'] ?? 'http://localhost:11434';
  static String get ollamaModel => dotenv.env['OLLAMA_MODEL_NAME'] ?? 'gemma-3n-E2B';

  /// Check if Ollama is configured
  static bool get isOllamaConfigured => ollamaHost.isNotEmpty && ollamaModel.isNotEmpty;

  /// Ollama timeout settings
  static int get ollamaTimeoutSeconds => int.tryParse(dotenv.env['OLLAMA_TIMEOUT_SECONDS'] ?? '600') ?? 600;
  static int get ollamaConnectionTimeout => int.tryParse(dotenv.env['OLLAMA_CONNECTION_TIMEOUT'] ?? '30') ?? 30;
  static int get ollamaImageTimeout => int.tryParse(dotenv.env['OLLAMA_IMAGE_TIMEOUT'] ?? '900') ?? 900;

  /// Legacy Google AI API Key for Gemini models (fallback only)
  static String get googleAI => dotenv.env['GEMINI_API_KEY'] ?? '';

  /// Check if Google AI API key is configured (fallback only)
  static bool get isGoogleAIConfigured =>
      googleAI.isNotEmpty && googleAI != 'YOUR_GOOGLE_AI_API_KEY_HERE';

  /// Get Gemini model name from environment (fallback only)
  static String get geminiModel => dotenv.env['GEMINI_MODEL_NAME'] ?? 'gemini-1.5-flash';

  /// Get temperature setting from environment
  static double get temperature =>
      double.tryParse(dotenv.env['TEMPERATURE'] ?? '0.1') ?? 0.1;

  /// Get max tokens from environment
  static int get maxTokens =>
      int.tryParse(dotenv.env['MAX_NEW_TOKENS'] ?? '2048') ?? 2048;

  /// Get debug mode setting
  static bool get debugMode =>
      dotenv.env['DEBUG_MODE']?.toLowerCase() == 'true';

  /// Get app name from environment
  static String get appName => dotenv.env['APP_NAME'] ?? 'SimulTrans AI';

  /// Get app version from environment
  static String get appVersion => dotenv.env['APP_VERSION'] ?? '1.0.0';
}

/// Instructions for configuration
class ApiKeyInstructions {
  static const String ollama = '''
🤖 Para configurar o Ollama (Recomendado - Offline):

1. 📥 Instale o Ollama: https://ollama.ai/download
2. 🚀 Inicie o Ollama: ollama serve
3. 📦 Baixe o modelo: ollama pull gemma3n:e2b
4. ✅ Verifique se está funcionando: ollama list

⚙️ Configurações no arquivo .env:
OLLAMA_HOST=http://localhost:11434
OLLAMA_MODEL_NAME=gemma3n:e2b
OLLAMA_TIMEOUT_SECONDS=600
OLLAMA_CONNECTION_TIMEOUT=30
OLLAMA_IMAGE_TIMEOUT=900

🎯 Modelos Gemma suportados via Ollama:
- gemma3n:e2b (recomendado - 2B parâmetros)
- gemma2:2b
- gemma2:9b
- gemma2:27b

✅ VANTAGENS DO OLLAMA:
- ✅ Totalmente offline
- ✅ Sem necessidade de API keys
- ✅ Privacidade total
- ✅ Sem limites de uso
- ✅ Suporte multimodal (texto + imagem)
''';

  static const String googleAI = '''
🔑 Configuração Google AI (Fallback apenas):

NOTA: O app agora usa Ollama por padrão. Esta configuração é apenas para fallback.

1. 🌐 Acesse: https://makersuite.google.com/app/apikey
2. 👤 Faça login com sua conta Google
3. ➕ Clique em "Create API Key"
4. 📋 Copie a chave gerada
5. 📁 Abra o arquivo .env na raiz do projeto
6. ✏️ Adicione: GEMINI_API_KEY=sua_chave_aqui

⚠️ IMPORTANTE:
- Use apenas como fallback se o Ollama não estiver disponível
- Requer conexão com internet
- Sujeito a limites de rate limiting
- Dados enviados para servidores Google
''';
}
