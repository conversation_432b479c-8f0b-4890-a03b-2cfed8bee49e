import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

import '../models/translation_result.dart';
import '../models/image_message.dart';
import '../config/api_keys.dart';
import '../utils/logger.dart';

/// Ollama service for offline translation using gemma3n:e2b model
class OllamaService {
  static final OllamaService _instance = OllamaService._internal();
  static OllamaService get instance => _instance;
  OllamaService._internal();

  http.Client? _httpClient;
  bool _isInitialized = false;
  String _currentModel = '';
  String _ollamaHost = '';
  int _timeoutSeconds = 600;
  int _connectionTimeout = 30;
  int _imageTimeout = 900;

  bool get isInitialized => _isInitialized;
  String get currentModel => _currentModel;
  String get ollamaHost => _ollamaHost;

  /// Initialize the Ollama service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Load environment variables
      _currentModel = dotenv.env['OLLAMA_MODEL_NAME'] ?? 'gemma3n:e2b';
      _ollamaHost = dotenv.env['OLLAMA_HOST'] ?? 'http://localhost:11434';
      _timeoutSeconds = int.tryParse(dotenv.env['OLLAMA_TIMEOUT_SECONDS'] ?? '600') ?? 600;
      _connectionTimeout = int.tryParse(dotenv.env['OLLAMA_CONNECTION_TIMEOUT'] ?? '30') ?? 30;
      _imageTimeout = int.tryParse(dotenv.env['OLLAMA_IMAGE_TIMEOUT'] ?? '900') ?? 900;

      // Initialize HTTP client
      _httpClient = http.Client();

      // Test connection to Ollama
      await _testConnection();

      _isInitialized = true;
      if (kDebugMode) {
        print('✅ Ollama service initialized successfully');
        print('🤖 Using model: $_currentModel');
        print('🌐 Ollama host: $_ollamaHost');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize Ollama service: $e');
      }
      throw Exception('Failed to initialize Ollama service: $e');
    }
  }

  /// Test connection to Ollama server
  Future<void> _testConnection() async {
    try {
      final response = await _httpClient!
          .get(
            Uri.parse('$_ollamaHost/api/tags'),
          )
          .timeout(Duration(seconds: _connectionTimeout));

      if (response.statusCode != 200) {
        throw Exception('Ollama server not responding. Status: ${response.statusCode}');
      }

      // Check if our model is available
      final data = jsonDecode(response.body);
      final models = data['models'] as List<dynamic>? ?? [];
      final modelExists = models.any((model) => model['name'] == _currentModel);

      if (!modelExists) {
        if (kDebugMode) {
          print('⚠️ Model $_currentModel not found. Available models:');
          for (final model in models) {
            print('  - ${model['name']}');
          }
        }
        throw Exception('Model $_currentModel not found in Ollama. Please pull the model first.');
      }

      if (kDebugMode) {
        print('✅ Ollama connection test successful');
        print('✅ Model $_currentModel is available');
      }

    } catch (e) {
      throw Exception('Failed to connect to Ollama: $e');
    }
  }

  /// Translate text using Ollama
  Future<TranslationResult> translateText({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
    String? context,
    String? domain,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    final startTime = DateTime.now();

    try {
      final prompt = _buildTranslationPrompt(
        text: text,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        context: context,
        domain: domain,
      );

      final response = await _generateCompletion(
        prompt: prompt,
        timeout: Duration(seconds: _timeoutSeconds),
      );

      final translatedText = _extractTranslation(response);
      final confidence = _calculateConfidence(response, text);

      final duration = DateTime.now().difference(startTime);

      return TranslationResult(
        originalText: text,
        translatedText: translatedText,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        confidence: confidence,
        timestamp: DateTime.now(),
        processingTime: duration,
        metadata: {
          'model': _currentModel,
          'method': 'ollama_text_translation',
          'host': _ollamaHost,
          'processing_time_ms': duration.inMilliseconds,
          'service': 'ollama_offline',
        },
      );

    } catch (e) {
      Logger.error('Ollama text translation failed: $e');
      return _createErrorResult(text, sourceLanguage, targetLanguage, e.toString());
    }
  }

  /// Translate image using Ollama with vision capabilities
  Future<TranslationResult> translateImage({
    required Uint8List imageBytes,
    required String targetLanguage,
    String? sourceLanguage,
    String? additionalContext,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    final startTime = DateTime.now();

    try {
      // Convert image to base64
      final base64Image = base64Encode(imageBytes);

      final prompt = _buildImageTranslationPrompt(
        targetLanguage: targetLanguage,
        sourceLanguage: sourceLanguage,
        additionalContext: additionalContext,
      );

      final response = await _generateVisionCompletion(
        prompt: prompt,
        imageBase64: base64Image,
        timeout: Duration(seconds: _imageTimeout),
      );

      final result = _parseImageTranslationResponse(response, targetLanguage, sourceLanguage);
      final duration = DateTime.now().difference(startTime);
      result.processingTime = duration;

      // Update metadata
      result.metadata?.addAll({
        'model': _currentModel,
        'method': 'ollama_image_translation',
        'host': _ollamaHost,
        'processing_time_ms': duration.inMilliseconds,
        'service': 'ollama_offline',
        'image_size_bytes': imageBytes.length,
      });

      return result;

    } catch (e) {
      Logger.error('Ollama image translation failed: $e');
      return _createErrorResult(
        'Image content',
        sourceLanguage ?? 'auto',
        targetLanguage,
        e.toString(),
      );
    }
  }

  /// Dispose resources
  void dispose() {
    _httpClient?.close();
    _httpClient = null;
    _isInitialized = false;
  }

  // Private helper methods

  /// Generate text completion using Ollama API
  Future<String> _generateCompletion({
    required String prompt,
    required Duration timeout,
  }) async {
    final requestBody = {
      'model': _currentModel,
      'prompt': prompt,
      'stream': false,
      'options': {
        'temperature': double.tryParse(dotenv.env['TEMPERATURE'] ?? '0.7') ?? 0.7,
        'top_p': double.tryParse(dotenv.env['TOP_P'] ?? '0.9') ?? 0.9,
        'max_tokens': int.tryParse(dotenv.env['MAX_NEW_TOKENS'] ?? '512') ?? 512,
      },
    };

    final response = await _httpClient!
        .post(
          Uri.parse('$_ollamaHost/api/generate'),
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode(requestBody),
        )
        .timeout(timeout);

    if (response.statusCode != 200) {
      throw Exception('Ollama API error: ${response.statusCode} - ${response.body}');
    }

    final data = jsonDecode(response.body);
    return data['response'] ?? '';
  }

  /// Generate vision completion using Ollama API with image
  Future<String> _generateVisionCompletion({
    required String prompt,
    required String imageBase64,
    required Duration timeout,
  }) async {
    final requestBody = {
      'model': _currentModel,
      'prompt': prompt,
      'images': [imageBase64],
      'stream': false,
      'options': {
        'temperature': double.tryParse(dotenv.env['TEMPERATURE'] ?? '0.7') ?? 0.7,
        'top_p': double.tryParse(dotenv.env['TOP_P'] ?? '0.9') ?? 0.9,
        'max_tokens': int.tryParse(dotenv.env['MAX_NEW_TOKENS'] ?? '512') ?? 512,
      },
    };

    final response = await _httpClient!
        .post(
          Uri.parse('$_ollamaHost/api/generate'),
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode(requestBody),
        )
        .timeout(timeout);

    if (response.statusCode != 200) {
      throw Exception('Ollama Vision API error: ${response.statusCode} - ${response.body}');
    }

    final data = jsonDecode(response.body);
    return data['response'] ?? '';
  }

  /// Build translation prompt for text
  String _buildTranslationPrompt({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
    String? context,
    String? domain,
  }) {
    final contextInfo = context != null ? '\nContext: $context' : '';
    final domainInfo = domain != null ? '\nDomain: $domain' : '';

    return '''You are a professional translator. Translate the following text from $sourceLanguage to $targetLanguage.

Instructions:
- Provide only the translation, no explanations
- Maintain the original meaning and tone
- Preserve formatting and structure
- Use appropriate cultural adaptations$contextInfo$domainInfo

Text to translate:
$text

Translation:''';
  }

  /// Build translation prompt for images
  String _buildImageTranslationPrompt({
    required String targetLanguage,
    String? sourceLanguage,
    String? additionalContext,
  }) {
    final sourceLang = sourceLanguage ?? 'any language';
    final contextInfo = additionalContext != null ? '\nAdditional context: $additionalContext' : '';

    return '''You are a professional translator with vision capabilities. Analyze the image and:

1. Extract all visible text from the image
2. Detect the source language of the text
3. Translate the extracted text from $sourceLang to $targetLanguage
4. Provide the result in JSON format

Instructions:
- Extract text accurately, including signs, labels, documents, etc.
- Maintain original formatting and structure
- Use appropriate cultural adaptations$contextInfo

Respond in this JSON format:
{
  "extracted_text": "original text found in image",
  "detected_language": "detected language code",
  "translated_text": "translation in $targetLanguage"
}''';
  }

  /// Extract translation from response
  String _extractTranslation(String response) {
    // Clean up the response
    String cleaned = response.trim();

    // Remove common prefixes
    final prefixes = ['Translation:', 'Translated text:', 'Result:'];
    for (final prefix in prefixes) {
      if (cleaned.startsWith(prefix)) {
        cleaned = cleaned.substring(prefix.length).trim();
        break;
      }
    }

    return cleaned.isNotEmpty ? cleaned : response;
  }

  /// Parse image translation response
  TranslationResult _parseImageTranslationResponse(
    String response,
    String targetLanguage,
    String? sourceLanguage,
  ) {
    try {
      // Try to parse as JSON first
      final jsonMatch = RegExp(r'\{.*\}', dotAll: true).firstMatch(response);
      if (jsonMatch != null) {
        final jsonStr = jsonMatch.group(0)!;
        final data = jsonDecode(jsonStr);

        return TranslationResult(
          originalText: data['extracted_text'] ?? 'Text extracted from image',
          translatedText: data['translated_text'] ?? response,
          sourceLanguage: data['detected_language'] ?? sourceLanguage ?? 'auto',
          targetLanguage: targetLanguage,
          confidence: 0.85, // Default confidence for successful parsing
          timestamp: DateTime.now(),
          metadata: {
            'extracted_text': data['extracted_text'],
            'detected_language': data['detected_language'],
          },
        );
      }
    } catch (e) {
      // If JSON parsing fails, treat as plain text
    }

    // Fallback to plain text response
    return TranslationResult(
      originalText: 'Text extracted from image',
      translatedText: _extractTranslation(response),
      sourceLanguage: sourceLanguage ?? 'auto',
      targetLanguage: targetLanguage,
      confidence: 0.75, // Lower confidence for fallback
      timestamp: DateTime.now(),
      metadata: {'parsing_method': 'fallback'},
    );
  }

  /// Calculate confidence based on response quality
  double _calculateConfidence(String response, String originalText) {
    if (response.isEmpty) return 0.0;

    // Basic confidence calculation
    double confidence = 0.8; // Base confidence

    // Adjust based on response length relative to original
    final lengthRatio = response.length / originalText.length;
    if (lengthRatio > 0.5 && lengthRatio < 2.0) {
      confidence += 0.1;
    }

    // Check for common error indicators
    if (response.toLowerCase().contains('error') ||
        response.toLowerCase().contains('sorry') ||
        response.toLowerCase().contains('cannot')) {
      confidence -= 0.3;
    }

    return confidence.clamp(0.0, 1.0);
  }

  /// Create error result
  TranslationResult _createErrorResult(
    String originalText,
    String sourceLanguage,
    String targetLanguage,
    String error,
  ) {
    return TranslationResult(
      originalText: originalText,
      translatedText: 'Erro na tradução: $error',
      sourceLanguage: sourceLanguage,
      targetLanguage: targetLanguage,
      confidence: 0.0,
      timestamp: DateTime.now(),
      metadata: {'error': error, 'service': 'ollama_offline'},
    );
  }
}
